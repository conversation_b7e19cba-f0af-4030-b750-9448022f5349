package main

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"uniqdev/api-pos-web/app/config"
	"uniqdev/api-pos-web/core/log"
	"uniqdev/api-pos-web/core/util/cast"
	"uniqdev/api-pos-web/core/util/token"
	"uniqdev/api-pos-web/core/util/validation"
	"uniqdev/api-pos-web/domain"
	authHttp "uniqdev/api-pos-web/module/auth/delivery/http"
	authRepoMysql "uniqdev/api-pos-web/module/auth/repository/mysql"
	authUseCase "uniqdev/api-pos-web/module/auth/usecase"
	productCategoryHttp "uniqdev/api-pos-web/module/product_categories_category/delivery/http"
	productCategoryRepoMysql "uniqdev/api-pos-web/module/product_categories_category/repository/mysql"
	productCategoryUseCase "uniqdev/api-pos-web/module/product_categories_category/usecase"
	productSubcategoryHttp "uniqdev/api-pos-web/module/product_categories_subcategory/delivery/http"
	productSubcategoryRepoMysql "uniqdev/api-pos-web/module/product_categories_subcategory/repository/mysql"
	productSubcategoryUseCase "uniqdev/api-pos-web/module/product_categories_subcategory/usecase"
	productTypeHttp "uniqdev/api-pos-web/module/product_categories_type/delivery/http"
	productTypeRepoMysql "uniqdev/api-pos-web/module/product_categories_type/repository/mysql"
	productTypeUseCase "uniqdev/api-pos-web/module/product_categories_type/usecase"
	productTaxGratuityHttp "uniqdev/api-pos-web/module/product_tax_gratuity/delivery/http"
	productTaxGratuityRepoMysql "uniqdev/api-pos-web/module/product_tax_gratuity/repository/mysql"
	productTaxGratuityUseCase "uniqdev/api-pos-web/module/product_tax_gratuity/usecase"
	productUnitHttp "uniqdev/api-pos-web/module/product_unit/delivery/http"
	productUnitRepoMysql "uniqdev/api-pos-web/module/product_unit/repository/mysql"
	productUnitUseCase "uniqdev/api-pos-web/module/product_unit/usecase"
	productPrcHttp "uniqdev/api-pos-web/module/purchase_report_category/delivery/http"
	productPrcRepoMysql "uniqdev/api-pos-web/module/purchase_report_category/repository/mysql"
	productPrcUseCase "uniqdev/api-pos-web/module/purchase_report_category/usecase"

	"github.com/dgrijalva/jwt-go"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/logger"
	jwtware "github.com/gofiber/jwt/v2"
	"github.com/joho/godotenv"
	"google.golang.org/grpc"

	authKrakendHttp "uniqdev/api-pos-web/module/auth_krakend/delivery/http"
	authKrakendRepoMysql "uniqdev/api-pos-web/module/auth_krakend/repository/mysql"
	authKrakendUseCase "uniqdev/api-pos-web/module/auth_krakend/usecase"
)

func init() {
	err := godotenv.Load()
	if err != nil {
		fmt.Println("error loading .env file")
	}
	log.AddHook(&log.SlackHook{
		HookUrl: os.Getenv("SLACK_HOOK_URL"),
		Channel: os.Getenv("SLACK_CHANNEL"),
	})
}

func main() {
	//get connetion
	//gormConn := config.GetGormConn()
	dbConn := config.GetMySqlConn()
	defer config.CloseMySqlConn(dbConn)

	//load usecase (sample)
	//productUseCase := usecase.NewProductUseCase(gorm.NewGormProductRepository(gormConn)) //use gorm
	//productUseCase := usecase.NewProductUseCase(mysql.NewMySqlProductRepository(dbConn)) //use native

	//load usecase
	authUseCase := authUseCase.AuthUseCase(authRepoMysql.AuthRepository(dbConn))
	productTypeUseCaseFlow := productTypeUseCase.ProductTypeUseCase(productTypeRepoMysql.ProductTypeRepository(dbConn))
	productCategoryUseCaseFlow := productCategoryUseCase.ProductCategoryUseCase(productCategoryRepoMysql.ProductCategoryRepository(dbConn))
	productSubcategoryUseCase := productSubcategoryUseCase.ProductSubcategoryUseCase(productSubcategoryRepoMysql.ProductSubcategoryRepository(dbConn))
	productPurchaseReportCategoryUseCase := productPrcUseCase.PurchaseReportCategoryUseCase(productPrcRepoMysql.PurchaseReportCategoryRepository(dbConn))
	productUnitUseCase := productUnitUseCase.ProductUnitUseCase(productUnitRepoMysql.ProductUnitRepository(dbConn))
	productTaxGratuityUseCase := productTaxGratuityUseCase.TaxGratuityUseCase(productTaxGratuityRepoMysql.TaxGratuityRepository(dbConn))

	// Initialize reset password components
	resetPasswordRepository := authKrakendRepoMysql.ResetPasswordRepository(dbConn)
	resetPasswordUseCase := authKrakendUseCase.ResetPasswordUseCase(resetPasswordRepository)

	authKrakendUseCase := authKrakendUseCase.AuthKrakendUseCase(
		authKrakendRepoMysql.AuthKrakendRepository(dbConn),
		authRepoMysql.AuthRepository(dbConn),
		resetPasswordUseCase,
	)

	//http ------
	app := fiber.New()
	app.Use(logger.New())
	app.Static("/", "./app/static")
	app.Get("/", func(c *fiber.Ctx) error {
		return c.SendString("hello world")
	})

	// Allow CORS
	//app.Use(cors.New())

	// Get User Language by Header
	app.Use(func(c *fiber.Ctx) error {
		allowLang := c.AcceptsLanguages("id", "en")

		langDefault := "en"
		lang := cast.ToString(c.Request().Header.Peek("Accept-Language"))
		if allowLang != "" {
			if lang != "" {
				fmt.Printf("load lang %s\n", lang)
				validation.Language = lang
			}
		} else {
			fmt.Printf("lang %s not found, use default lang [EN]\n", lang)
			lang = langDefault
		}

		return c.Next()
	})

	authHttp.AuthHandler(app, authUseCase)
	authKrakendHttp.AuthKrakendHandler(app, authKrakendUseCase)

	//manual JWT token decode
	app.Use(func(c *fiber.Ctx) error {
		requestToken := c.Get("Authorization")
		tokenData := strings.Split(requestToken, ".")
		if len(tokenData) != 3 {
			return c.SendStatus(401)
		}

		//get token data
		tokenBody := tokenData[1] //jwt's payload

		//decode token
		decoded, _ := base64.RawStdEncoding.DecodeString(tokenBody)
		decodedJson := string(decoded)
		claims, _ := cast.JsonToMap(decodedJson)
		claims = claims["data"].(map[string]interface{})
		dataUser, _ := json.Marshal(claims["user"])
		dataRole, _ := json.Marshal(claims["user_role"])

		//share token data
		var (
			user domain.User
			role domain.UserRole
		)

		// token data :: user
		cast.JsonToStruct(dataUser, &user)
		token.UserData = user

		// token data :: user_role
		cast.JsonToStruct(dataRole, &role)
		token.UserRole = role

		if user.UserId == "" {
			fmt.Println("invalid token on manual decode token")
			return c.SendStatus(401)
		}

		return c.Next()
	})

	//http JWT middleware, all route after this will use JWT authentication
	app.Use(jwtware.New(jwtware.Config{
		SigningKey: []byte("secret"),
		SuccessHandler: func(c *fiber.Ctx) error {
			//get token data
			claims := c.Locals("user").(*jwt.Token).Claims.(jwt.MapClaims)
			claims = claims["data"].(map[string]interface{})
			userDataJson, _ := json.Marshal(claims["user"])
			userRoleDataJson, _ := json.Marshal(claims["user_role"])

			//share token data
			var user domain.User
			cast.JsonToStruct(userDataJson, &user)
			token.UserData = user

			//share user role
			var role domain.UserRole
			cast.JsonToStruct(userRoleDataJson, &role)
			token.UserRole = role

			//check if token invalid format
			if user.UserId == "" {
				return c.SendStatus(401)
			}

			//log if request is POST
			if c.Method() == "POST" {
				fmt.Println(string(c.Body()))
			}

			return c.Next()
		},
		ErrorHandler: func(ctx *fiber.Ctx, err error) error {
			return ctx.SendStatus(401)
		},
	}))

	//define other route
	productTypeHttp.ProductTypeHandler(app, productTypeUseCaseFlow)
	productCategoryHttp.ProductCategoryHandler(app, productCategoryUseCaseFlow)
	productSubcategoryHttp.ProductSubcategoryHandler(app, productSubcategoryUseCase)
	productPrcHttp.PurchaseReportCategoryHandler(app, productPurchaseReportCategoryUseCase)
	productUnitHttp.ProductUnitHandler(app, productUnitUseCase)
	productTaxGratuityHttp.TaxGratuityHandler(app, productTaxGratuityUseCase)
	//productHttp.NewProductHandler(app, productUseCase) //it just sample

	defer fmt.Println("service ended...")
	errs := make(chan error)

	go func() {
		c := make(chan os.Signal, 1)
		signal.Notify(c, syscall.SIGINT, syscall.SIGTERM)
		errs <- fmt.Errorf("%s", <-c)
	}()

	//grpc -------
	lis, err := net.Listen("tcp", ":3001")
	if err != nil {
		panic(err)
	}
	s := grpc.NewServer()
	//productGrpc.NewProductHandler(s, productUseCase) //it just sample
	go func() {
		errs <- s.Serve(lis)
	}()

	go func() {
		port := "3000"
		if os.Getenv("PORT") != "" {
			port = os.Getenv("PORT")
		}
		errs <- app.Listen(":" + port)
	}()

	fmt.Printf("exit: %v", <-errs)
}
