package domain

type Auth struct{}
type AuthUseCase interface {
	CreateToken(AuthInputLogin) (UserToken, error)
	TokenByPHPSession(sessionID string) (UserToken, error)
	TokenByRefreshToken(refreshToken string) (UserToken, error)
}

type AuthRepository interface {
	FindAccount(userType, email string) (User, string, error)
	GetOutletAccess(user User) (string, error)
	FindSession(sessionID string) (User, error)
	FindAccountByUserTypeUserId(userType, userId string) (User, error)
	SaveRefreshToken(generatedRefreshToken string) error
	FindRefreshToken(refreshTokenJTI string) (User, error)
	FindMultiAccountByEmail(email string) ([]User, error)
	FindMultiAccount(accountId int) ([]User, error)
	FindAccountById(userType string, userId string) (User, error)
}

type AuthInputLogin struct {
	Email    string `json:"email" xml:"email" form:"email" validate:"required,email"`
	Password string `json:"password" xml:"password" form:"password" validate:"required"`
	UserType string `json:"user_type" xml:"user_type" form:"user_type"`
}
