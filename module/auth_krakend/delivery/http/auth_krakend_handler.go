package http

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
	"uniqdev/api-pos-web/core/log"
	"uniqdev/api-pos-web/core/util/cast"
	"uniqdev/api-pos-web/core/util/validation"
	domain "uniqdev/api-pos-web/domain"

	v2 "github.com/gofiber/fiber/v2"
)

type authKrakendHandler struct {
	domain.AuthKrakendUseCase
}

func AuthKrakendHandler(app *v2.App, useCase domain.AuthKrakendUseCase) {
	handler := &authKrakendHandler{useCase}
	app.Get("/module/authKrakend", handler.Sample)

	//app.Post("/v1/auth_krakend/login", handler.Login2)
	v1 := app.Group("/v1/auth_krakend")
	v1.Post("/login", handler.Login)

	v1.Post("/login/social/:socialMedia", handler.LoginWithSocial)

	//login callback
	v1.Post("/callback/:platform/:id", handler.LoginCallback)
	v1.Get("/callback/:platform/:id", handler.LoginCallback)

	v1.Post("/refresh", handler.RefreshToken)
	v1.Get("/session", handler.Session)

	v1.Get("/account", handler.FetchUserAccounts)
	v1.Post("/account", handler.FetchTokenByAccount)

	// Reset password routes
	v1.Post("/reset-password", handler.RequestPasswordReset)
	v1.Post("/reset-password/verify", handler.VerifyPasswordResetOTP)
	v1.Post("/reset-password/update", handler.UpdatePasswordWithReset)
}

func (h authKrakendHandler) Sample(c *v2.Ctx) error {
	return c.SendString("this is sample of authKrakend feature route")
}

func (h authKrakendHandler) Login(c *v2.Ctx) error {
	var inputLogin domain.AuthInputLogin
	c.BodyParser(&inputLogin)
	response, err := h.AuthKrakendUseCase.CreateTokenByLogin(inputLogin)
	if err != nil {
		fmt.Println("login err: ", err)
		errStr := cast.ToString(err)
		if validation.IsJson(errStr) {
			c.Status(400)
			return c.JSON(cast.JsonToStruct(errStr, inputLogin))
		}
		switch errStr {
		case "401":
			return c.SendStatus(401)
		case "404":
			return c.SendStatus(404)
		default:
			fmt.Println("login error: ", err)
			return c.SendStatus(422)
		}
	}

	return c.JSON(response)
}

func (h authKrakendHandler) RefreshToken(c *v2.Ctx) error {
	refreshToken := c.FormValue("refresh_token")
	response, err := h.CreateTokenByRefreshToken(refreshToken)
	if err != nil {
		errStr := cast.ToString(err)
		switch errStr {
		case "401":
			return c.SendStatus(401)
		case "404":
			return c.SendStatus(404)
		default:
			fmt.Println("login with refreshToken error: ", err)
			return c.SendStatus(422)
		}
	}
	return c.JSON(response)
}

func (h authKrakendHandler) Session(c *v2.Ctx) error {
	sID := c.Request().Header.Peek("Authorization")
	sessID := cast.ToString(sID)
	response, err := h.AuthKrakendUseCase.CreateTokenByPHPSession(sessID)
	if err != nil {
		errStr := cast.ToString(err)
		switch errStr {
		case "401":
			return c.SendStatus(401)
		case "404":
			return c.SendStatus(404)
		default:
			fmt.Println("login with session error: ", err)
			return c.SendStatus(422)
		}
	}
	return c.JSON(response)
}

func (h authKrakendHandler) FetchUserAccounts(ctx *v2.Ctx) error {
	requestToken := ctx.Get("Authorization")
	tokenData := strings.Split(requestToken, ".")
	if len(tokenData) != 3 {
		return ctx.SendStatus(401)
	}

	//get token data
	tokenBody := tokenData[1] //jwt's payload

	//decode token
	decoded, _ := base64.RawStdEncoding.DecodeString(tokenBody)
	decodedJson := string(decoded)
	claims, _ := cast.JsonToMap(decodedJson)
	claims = claims["data"].(map[string]interface{})

	dataUser, _ := json.Marshal(claims["user"])
	var user domain.User
	cast.JsonToStruct(dataUser, &user)

	fmt.Println("gettting user account of ", user.Email, user.AccountId)

	result, err := h.AuthKrakendUseCase.FetchUserAccounts(user.AccountId, user.Email)
	if err != nil {
		fmt.Println("FetchUserAccounts err: ", err)
		return err
	}

	return ctx.JSON(result)
}

func (h authKrakendHandler) FetchTokenByAccount(ctx *v2.Ctx) error {
	//userId := ctx.FormValue("user_id")
	//userType := ctx.FormValue("user_type")

	requestToken := ctx.Get("Authorization")
	tokenData := strings.Split(requestToken, ".")
	if len(tokenData) != 3 {
		fmt.Println("invalid aut: ", requestToken)
		return ctx.SendStatus(401)
	}

	//get token data
	tokenBody := tokenData[1] //jwt's payload

	//decode token
	decoded, _ := base64.RawStdEncoding.DecodeString(tokenBody)
	decodedJson := string(decoded)
	claims, _ := cast.JsonToMap(decodedJson)
	claims = claims["data"].(map[string]interface{})

	dataUser, _ := json.Marshal(claims["user"])
	var user domain.User
	cast.JsonToStruct(dataUser, &user)

	var userInput struct {
		UserId   string `json:"user_id" form:"user_id"`
		UserType string `json:"user_type" form:"user_type"`
	}

	err := ctx.BodyParser(&userInput)
	log.IfError(err)

	log.Info("get account auth: %s (%s)", userInput.UserId, userInput.UserType)
	log.Info("user session: %v", user)

	result, err := h.AuthKrakendUseCase.CreateTokenByAccount(userInput.UserId, userInput.UserType, user.AccountId)
	if err != nil {
		fmt.Println("CreateTokenByAccount err: ", err)
		errStr := cast.ToString(err)
		switch errStr {
		case "401":
			return ctx.SendStatus(401)
		case "404":
			return ctx.SendStatus(404)
		case "403":
			return ctx.SendStatus(403)
		default:
			fmt.Println("login error: ", err)
			return ctx.SendStatus(422)
		}
	}

	return ctx.JSON(result)
}

func (h authKrakendHandler) LoginWithSocial(ctx *v2.Ctx) error {
	var loginSocial domain.LoginSocial
	err := ctx.BodyParser(&loginSocial)
	log.IfError(err)

	socialMedia := ctx.Params("socialMedia")
	fmt.Println("login with:", socialMedia)

	result, err := h.AuthKrakendUseCase.CreateTokenByLoginSocial(loginSocial, socialMedia)
	if err != nil {
		errStr := cast.ToString(err)
		switch errStr {
		case "401":
			return ctx.SendStatus(401)
		case "404":
			return ctx.SendStatus(404)
		default:
			fmt.Println("login with session error: ", err)
			return ctx.SendStatus(422)
		}
	}
	return ctx.JSON(result)
}

func (a authKrakendHandler) LoginCallback(ctx *v2.Ctx) error {
	platform := ctx.Params("platform")
	data := map[string]interface{}{
		"status":   "success",
		"platform": platform,
		"method":   ctx.Method(),
		"body":     string(ctx.Body()),
	}
	fmt.Println("login callback: ", cast.ToString(data))

	return ctx.Redirect("https://task-app.uniqdev.xyz/login", 307)
	// return ctx.JSON(data)
}

// Reset password handlers
func (h authKrakendHandler) RequestPasswordReset(c *v2.Ctx) error {
	var input domain.ResetPasswordInput

	// Parse request body
	if err := c.BodyParser(&input); err != nil {
		return c.Status(400).JSON(domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInvalidToken,
			Message:   "Invalid request format",
		})
	}

	// Validate input
	errList := validation.Struct(input)
	if validation.IsError(errList) {
		return c.Status(400).JSON(domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInvalidToken,
			Message:   "Validation failed",
			Details:   cast.ToString(errList),
		})
	}

	// Call usecase
	response, err := h.AuthKrakendUseCase.RequestPasswordReset(input)
	if err != nil {
		fmt.Println("RequestPasswordReset error: ", err)
		return c.Status(500).JSON(domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInternalError,
			Message:   domain.MsgInternalError,
		})
	}

	// Return appropriate status code
	statusCode := 200
	if !response.Success {
		switch response.ErrorCode {
		case domain.ErrorEmailNotFound:
			statusCode = 404
		case domain.ErrorEmailRateLimited:
			statusCode = 429
		default:
			statusCode = 400
		}
	}

	return c.Status(statusCode).JSON(response)
}

func (h authKrakendHandler) VerifyPasswordResetOTP(c *v2.Ctx) error {
	var input domain.ResetPasswordInput

	// Parse request body
	if err := c.BodyParser(&input); err != nil {
		return c.Status(400).JSON(domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInvalidToken,
			Message:   "Invalid request format",
		})
	}

	// Validate input
	errList := validation.Struct(input)
	if validation.IsError(errList) {
		return c.Status(400).JSON(domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInvalidToken,
			Message:   "Validation failed",
			Details:   cast.ToString(errList),
		})
	}

	// Call usecase
	response, err := h.AuthKrakendUseCase.VerifyPasswordResetOTP(input)
	if err != nil {
		fmt.Println("VerifyPasswordResetOTP error: ", err)
		return c.Status(500).JSON(domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInternalError,
			Message:   domain.MsgInternalError,
		})
	}

	// Return appropriate status code
	statusCode := 200
	if !response.Success {
		switch response.ErrorCode {
		case domain.ErrorInvalidOTP, domain.ErrorOTPExpired:
			statusCode = 400
		case domain.ErrorInvalidToken, domain.ErrorTokenExpired:
			statusCode = 401
		default:
			statusCode = 400
		}
	}

	return c.Status(statusCode).JSON(response)
}

func (h authKrakendHandler) UpdatePasswordWithReset(c *v2.Ctx) error {
	var input domain.ResetPasswordInput

	// Parse request body
	if err := c.BodyParser(&input); err != nil {
		return c.Status(400).JSON(domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInvalidToken,
			Message:   "Invalid request format",
		})
	}

	// Validate input
	errList := validation.Struct(input)
	if validation.IsError(errList) {
		return c.Status(400).JSON(domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInvalidToken,
			Message:   "Validation failed",
			Details:   cast.ToString(errList),
		})
	}

	// Call usecase
	response, err := h.AuthKrakendUseCase.UpdatePasswordWithReset(input)
	if err != nil {
		fmt.Println("UpdatePasswordWithReset error: ", err)
		return c.Status(500).JSON(domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInternalError,
			Message:   domain.MsgInternalError,
		})
	}

	// Return appropriate status code
	statusCode := 200
	if !response.Success {
		switch response.ErrorCode {
		case domain.ErrorPasswordTooWeak, domain.ErrorPasswordsDontMatch:
			statusCode = 400
		case domain.ErrorInvalidToken, domain.ErrorTokenExpired:
			statusCode = 401
		default:
			statusCode = 400
		}
	}

	return c.Status(statusCode).JSON(response)
}
