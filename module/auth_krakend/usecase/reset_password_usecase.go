package usecase

import (
	"errors"
	"fmt"
	"os"
	"strings"
	"time"
	"uniqdev/api-pos-web/core/util"
	domain "uniqdev/api-pos-web/domain"
)

type resetPasswordUseCase struct {
	resetPasswordRepository domain.ResetPasswordRepository
}

func ResetPasswordUseCase(repository domain.ResetPasswordRepository) domain.ResetPasswordUseCase {
	return &resetPasswordUseCase{repository}
}

func (u resetPasswordUseCase) RequestReset(input domain.ResetPasswordInput) (domain.ResetPasswordResponse, error) {
	// Validate input
	if input.Email == "" {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorEmailNotFound,
			Message:   domain.MsgEmailNotFound,
		}, nil
	}

	// Check rate limiting
	err := u.resetPasswordRepository.CheckResetRateLimit(input.Email)
	if err != nil {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorEmailRateLimited,
			Message:   domain.MsgEmailRateLimited,
		}, nil
	}

	// Check if email exists
	_, err = u.resetPasswordRepository.FindAccountByEmail(input.Email)
	if err != nil {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorEmailNotFound,
			Message:   domain.MsgEmailNotFound,
		}, nil
	}

	// Generate 6-digit OTP
	otpCode, err := util.GenerateOTP()
	if err != nil {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInternalError,
			Message:   domain.MsgInternalError,
		}, errors.New("failed to generate OTP")
	}

	// Hash the OTP code
	hashedOTP := util.HashSHA256(otpCode)

	// Save OTP to database (3 minutes expiry)
	expiredAt := time.Now().Add(3 * time.Minute).Unix()
	authOtpId, err := u.resetPasswordRepository.SaveOTP(input.Email, "email", hashedOTP, expiredAt)
	if err != nil {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInternalError,
			Message:   domain.MsgInternalError,
		}, errors.New("failed to save OTP")
	}

	// Create verification token
	encryptionKey := os.Getenv("RESET_PASSWORD_ENCRYPTION_KEY")
	if encryptionKey == "" {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInternalError,
			Message:   domain.MsgInternalError,
		}, errors.New("encryption key not configured")
	}

	verificationToken, err := util.CreateVerificationToken(authOtpId, time.Now().Unix(), encryptionKey)
	if err != nil {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInternalError,
			Message:   domain.MsgInternalError,
		}, errors.New("failed to create verification token")
	}

	// Send email via scheduled_message
	emailTitle := "Password Reset Code"
	emailMessage := fmt.Sprintf("Your password reset code is: %s. This code will expire in 3 minutes.", otpCode)
	err = u.resetPasswordRepository.SaveScheduledMessage(emailTitle, emailMessage, input.Email, "email", time.Now().Unix())
	if err != nil {
		fmt.Printf("Warning: Failed to schedule email: %v\n", err)
		// Don't fail the request if email scheduling fails
	}

	return domain.ResetPasswordResponse{
		Success:           true,
		Message:           domain.MsgVerificationSent,
		VerificationToken: verificationToken,
	}, nil
}

func (u resetPasswordUseCase) VerifyOTP(input domain.ResetPasswordInput) (domain.ResetPasswordResponse, error) {
	// Validate input
	if input.OTPCode == "" || input.VerificationToken == "" {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInvalidToken,
			Message:   domain.MsgInvalidToken,
		}, nil
	}

	// Decrypt verification token
	encryptionKey := os.Getenv("RESET_PASSWORD_ENCRYPTION_KEY")
	if encryptionKey == "" {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInternalError,
			Message:   domain.MsgInternalError,
		}, errors.New("encryption key not configured")
	}

	authOtpId, _, err := util.DecryptVerificationToken(input.VerificationToken, encryptionKey)
	if err != nil {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInvalidToken,
			Message:   domain.MsgInvalidToken,
		}, nil
	}

	// Find OTP record
	otpRecord, err := u.resetPasswordRepository.FindOTPByID(authOtpId)
	if err != nil {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInvalidOTP,
			Message:   domain.MsgInvalidOTP,
		}, nil
	}

	// Check if OTP is expired
	if time.Now().Unix() > otpRecord.DateExpired {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorOTPExpired,
			Message:   domain.MsgOTPExpired,
		}, nil
	}

	// Check if OTP is already used
	if otpRecord.AuthenticatedAt != nil {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInvalidOTP,
			Message:   domain.MsgInvalidOTP,
		}, nil
	}

	// Verify OTP code
	hashedInputOTP := util.HashSHA256(input.OTPCode)
	if hashedInputOTP != otpRecord.Token {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInvalidOTP,
			Message:   domain.MsgInvalidOTP,
		}, nil
	}

	// Mark OTP as authenticated
	err = u.resetPasswordRepository.MarkOTPAsAuthenticated(authOtpId)
	if err != nil {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInternalError,
			Message:   domain.MsgInternalError,
		}, errors.New("failed to mark OTP as authenticated")
	}

	// Generate reset session
	sessionId := util.HashMd5(fmt.Sprintf("reset_%s_%d", otpRecord.Contact, time.Now().UnixNano()))
	resetTokenRaw, err := util.GenerateSecureID(32)
	if err != nil {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInternalError,
			Message:   domain.MsgInternalError,
		}, errors.New("failed to generate reset token")
	}

	// Hash the reset token for database storage
	hashedResetToken, err := util.BcryptHash(resetTokenRaw)
	if err != nil {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInternalError,
			Message:   domain.MsgInternalError,
		}, errors.New("failed to hash reset token")
	}

	// Save reset session (15 minutes expiry)
	expiredAt := time.Now().Add(15 * time.Minute).Unix()
	err = u.resetPasswordRepository.SaveResetSession(sessionId, otpRecord.Contact, hashedResetToken, expiredAt)
	if err != nil {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInternalError,
			Message:   domain.MsgInternalError,
		}, errors.New("failed to save reset session")
	}

	// Create encrypted reset token for client
	resetToken, err := util.CreateResetToken(sessionId, time.Now().Unix(), encryptionKey)
	if err != nil {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInternalError,
			Message:   domain.MsgInternalError,
		}, errors.New("failed to create reset token")
	}

	return domain.ResetPasswordResponse{
		Success:    true,
		Message:    domain.MsgCodeVerified,
		ResetToken: resetToken,
	}, nil
}

func (u resetPasswordUseCase) UpdatePassword(input domain.ResetPasswordInput) (domain.ResetPasswordResponse, error) {
	// Validate input
	if input.NewPassword == "" || input.ConfirmPassword == "" || input.ResetToken == "" {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInvalidToken,
			Message:   domain.MsgInvalidToken,
		}, nil
	}

	// Check if passwords match
	if input.NewPassword != input.ConfirmPassword {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorPasswordsDontMatch,
			Message:   domain.MsgPasswordsDontMatch,
		}, nil
	}

	// Validate password strength
	err := util.ValidatePassword(input.NewPassword)
	if err != nil {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorPasswordTooWeak,
			Message:   domain.MsgPasswordTooWeak,
			Details:   err.Error(),
		}, nil
	}

	// Decrypt reset token
	encryptionKey := os.Getenv("RESET_PASSWORD_ENCRYPTION_KEY")
	if encryptionKey == "" {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInternalError,
			Message:   domain.MsgInternalError,
		}, errors.New("encryption key not configured")
	}

	sessionId, _, err := util.DecryptResetToken(input.ResetToken, encryptionKey)
	if err != nil {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInvalidToken,
			Message:   domain.MsgInvalidToken,
		}, nil
	}

	// Find reset session
	resetSession, err := u.resetPasswordRepository.FindResetSession(sessionId)
	if err != nil {
		if strings.Contains(err.Error(), "expired") {
			return domain.ResetPasswordResponse{
				Success:   false,
				ErrorCode: domain.ErrorTokenExpired,
				Message:   domain.MsgTokenExpired,
			}, nil
		}
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInvalidToken,
			Message:   domain.MsgInvalidToken,
		}, nil
	}

	// Hash new password
	hashedPassword, err := util.BcryptHash(input.NewPassword)
	if err != nil {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInternalError,
			Message:   domain.MsgInternalError,
		}, errors.New("failed to hash password")
	}

	// Update password in accounts table
	err = u.resetPasswordRepository.UpdateAccountPassword(resetSession.Email, hashedPassword)
	if err != nil {
		return domain.ResetPasswordResponse{
			Success:   false,
			ErrorCode: domain.ErrorInternalError,
			Message:   domain.MsgInternalError,
		}, errors.New("failed to update password")
	}

	// Invalidate all user sessions
	err = u.resetPasswordRepository.InvalidateUserSessions(resetSession.Email)
	if err != nil {
		fmt.Printf("Warning: Failed to invalidate user sessions: %v\n", err)
		// Don't fail the request if session invalidation fails
	}

	// Clean up reset session
	err = u.resetPasswordRepository.DeleteResetSession(sessionId)
	if err != nil {
		fmt.Printf("Warning: Failed to delete reset session: %v\n", err)
		// Don't fail the request if cleanup fails
	}

	// Send confirmation email
	emailTitle := "Password Reset Successful"
	emailMessage := "Your password has been successfully reset. If you did not request this change, please contact support immediately."
	err = u.resetPasswordRepository.SaveScheduledMessage(emailTitle, emailMessage, resetSession.Email, "email", time.Now().Unix())
	if err != nil {
		fmt.Printf("Warning: Failed to schedule confirmation email: %v\n", err)
		// Don't fail the request if email scheduling fails
	}

	return domain.ResetPasswordResponse{
		Success: true,
		Message: domain.MsgPasswordUpdated,
	}, nil
}
