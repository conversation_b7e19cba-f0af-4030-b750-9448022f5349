package usecase

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"uniqdev/api-pos-web/core/log"
	"uniqdev/api-pos-web/core/util"
	"uniqdev/api-pos-web/core/util/cast"
	"uniqdev/api-pos-web/core/util/validation"
	domain "uniqdev/api-pos-web/domain"
)

type authKrakendUseCase struct {
	repokrakend domain.AuthKrakendRepository
	domain.AuthRepository
	resetPasswordUseCase domain.ResetPasswordUseCase
}

func AuthKrakendUseCase(repository domain.AuthKrakendRepository, authRepository domain.AuthRepository, resetPasswordUseCase domain.ResetPasswordUseCase) domain.AuthKrakendUseCase {
	return &authKrakendUseCase{repository, authRepository, resetPasswordUseCase}
}

func (u authKrakendUseCase) CreateTokenByLogin(loginInput domain.AuthInputLogin) (domain.AuthKrakend, error) {
	var authKrakend domain.AuthKrakend

	loginData, _ := json.Marshal(loginInput)
	fmt.Println("user login: ", string(loginData))

	//validation
	errList := validation.Struct(loginInput)
	if validation.IsError(errList) {
		return authKrakend, cast.ToError(errList)
	}

	//1. check user from DB
	userTypes := []string{"admin", "employee", "hrm"}

	//if user passed userType, used that instead
	if loginInput.UserType != "" {
		userTypes = []string{loginInput.UserType}
	}
	var user domain.User
	var pass string
	var err error
	for _, userType := range userTypes {
		user, pass, err = u.AuthRepository.FindAccount(userType, loginInput.Email)
		if err != nil {
			fmt.Println("FindAccount err: ", err)
			return authKrakend, err
		}

		if user.UserId != "" {
			break
		}
	}

	if user.UserId == "" {
		//account not found
		fmt.Println("account not found")
		return authKrakend, errors.New("404")
	}

	//2. validate user's password
	err = util.BcryptVerify(loginInput.Password, pass)
	if err != nil {
		//password invalid
		fmt.Println("password invalid")
		return authKrakend, errors.New("401")
	}

	//get generated response format
	authKrakend, err = u.GenerateToken(user)

	//get generated refresh token
	refreshToken, refreshTokenExpired := u.GenerateRefreshToken(user)

	//append generated refresh token to response format
	authKrakend.RefreshToken = refreshToken
	authKrakend.RefreshTokenExpired = refreshTokenExpired

	fmt.Printf("refresh token of '%s': %v\n", loginInput.Email, refreshToken)
	return authKrakend, err
}

func (u authKrakendUseCase) CreateTokenByLoginSocial(loginSocial domain.LoginSocial, socialMedia string) (domain.AuthKrakend, error) {
	fmt.Println("login with: ", socialMedia)
	if loginSocial.IdToken == "" {
		fmt.Println("id_token is empty...")
		return domain.AuthKrakend{}, errors.New("422")
	}

	var authSosmed SocialMediaAuth
	if socialMedia == "apple" {
		authSosmed = AuthWithApple{}
	} else {
		authSosmed = AuthWithGoogle{}
	}

	authResult, err := authSosmed.ValidateToken(loginSocial.IdToken)
	if err != nil {
		return domain.AuthKrakend{}, err
	}

	userEmail := authResult.Email

	//1. check user from DB
	user, _, err := u.AuthRepository.FindAccount("admin", userEmail)
	if user.UserId == "" {
		user, _, err = u.AuthRepository.FindAccount("employee", userEmail)
	}
	if user.UserId == "" {
		//account not found
		fmt.Println("account not found")
		return domain.AuthKrakend{}, errors.New("401")
	}

	//get generated response format
	authKrakend, err := u.GenerateToken(user)

	//get generated refresh token
	refreshToken, refreshTokenExpired := u.GenerateRefreshToken(user)

	//append generated refresh token to response format
	authKrakend.RefreshToken = refreshToken
	authKrakend.RefreshTokenExpired = refreshTokenExpired

	fmt.Printf("refresh token of '%s' (social: %s) : %v\n", userEmail, socialMedia, refreshToken)
	return authKrakend, err
}

func (u authKrakendUseCase) CreateTokenByRefreshToken(refreshToken string) (domain.AuthKrakend, error) {
	var authKrakend domain.AuthKrakend

	//validation
	if refreshToken == "" {
		fmt.Println("refreshToken sent by client is empty...")
		return authKrakend, errors.New("401")
	}

	fmt.Println("get token with refresh token : ", refreshToken)
	//1. find account by refresh token
	user, err := u.repokrakend.FindRefreshToken(refreshToken + ".refresh")
	if user.UserId == "" {
		if err != nil {
			// fmt.Printf("find refresh token err: %v | %v\n", err, refreshToken)
			log.IfError(fmt.Errorf("find refresh token err: %v | %v", err, refreshToken))
			return authKrakend, err
		}

		return authKrakend, errors.New("404")
	}

	fmt.Println("get token [success] with refresh token : ", refreshToken)

	//2. get current user data
	user, err = u.AuthRepository.FindAccountByUserTypeUserId(user.UserType, user.UserId)
	if user.Email == "" {
		return domain.AuthKrakend{}, fmt.Errorf("401")
	}

	//get generated response format
	authKrakend, err = u.GenerateToken(user)

	//get generated refresh token
	refreshToken, refreshTokenExpired := u.GenerateRefreshToken(user)

	//append generated refresh token to response format
	authKrakend.RefreshToken = refreshToken
	authKrakend.RefreshTokenExpired = refreshTokenExpired

	fmt.Printf("refresh token of '%s': %v\n", user.Email, refreshToken)
	return authKrakend, err
}

func (u authKrakendUseCase) CreateTokenByPHPSession(sessionID string) (domain.AuthKrakend, error) {
	var authKrakend domain.AuthKrakend

	//validation
	if sessionID == "" {
		fmt.Println("sessionID is not provided....")
		return authKrakend, errors.New("401")
	}

	//1. find account by session
	user, err := u.AuthRepository.FindSession(sessionID)
	if user.UserId == "" {
		fmt.Println("user not found....")
		return authKrakend, errors.New("404")
	}

	//2. get current user data
	user, err = u.AuthRepository.FindAccountByUserTypeUserId(user.UserType, user.UserId)
	if err != nil {
		fmt.Println("FindAccountByUserTypeUserId err", err)
	}

	//get generated response format
	authKrakend, err = u.GenerateToken(user)

	return authKrakend, err
}

func (u authKrakendUseCase) GenerateToken(user domain.User) (domain.AuthKrakend, error) {
	var authKrakend domain.AuthKrakend

	//3. get user's outlet access
	outletAccess, _ := u.AuthRepository.GetOutletAccess(user)

	//4. create payload data
	authKrakend.Data.User = user
	authKrakend.Data.UserRole.OutletAccess = outletAccess
	payloads := authKrakend.Data

	// 5. create payload data of jwt
	timeNow := util.CurrentTime() //time.Now().Unix()
	// expired := time.Now().Add(time.Hour * 24 * 3).Unix() //expire in 3 days
	expired := time.Now().Add(time.Hour * 5).Unix() //for testing
	payloadsJwt := map[string]interface{}{
		"nbf":  timeNow,
		"exp":  expired,
		"data": payloads,
		"iat":  timeNow,
		"jti":  util.HashMd5(user.UserType + "-" + user.UserId),
	}

	//prepare output
	authKrakend.AccessToken = payloadsJwt
	authKrakend.AccessTokenExpired = expired
	authKrakend.AccessTokenType = "Bearer"

	return authKrakend, nil
}

func (u authKrakendUseCase) GenerateRefreshToken(user domain.User) (token string, expired int64) {
	refreshTokenExpired := time.Now().Add(time.Hour * 24 * 30).Unix()
	timeNow := util.CurrentMillis()
	jti := util.HashMd5(user.UserType + "-" + user.UserId + "-" + cast.ToString(timeNow))

	//refresh token jwt payload
	refreshTokenData := map[string]interface{}{
		"iat":  timeNow,
		"nbf":  timeNow,
		"exp":  refreshTokenExpired,
		"data": user,
		"jti":  jti,
	}

	b, _ := json.Marshal(refreshTokenData)
	jsonData := cast.ToString(b)
	bcryptData, _ := util.BcryptHash(jsonData)
	refreshID := util.HashMd5(jsonData)

	u.repokrakend.SaveRefreshToken(refreshID, jsonData, bcryptData, refreshTokenExpired)
	return refreshID, refreshTokenExpired
}

func (u authKrakendUseCase) FetchUserAccounts(accountId string, email string) (interface{}, error) {
	accounts, err := u.FindMultiAccount(cast.ToInt(accountId))
	if err != nil {
		return nil, err
	}

	if len(accounts) == 0 {
		accounts, err = u.FindMultiAccountByEmail(email)
	}
	if err != nil {
		return nil, err
	}

	fmt.Println("multi-account of", accountId, " --> ", accounts)

	refreshTokenExpired := time.Now().Add(time.Minute * 5).Unix()
	timeNow := util.CurrentMillis()

	//refresh token jwt payload
	refreshTokenData := map[string]interface{}{
		"iat":  timeNow,
		"nbf":  timeNow,
		"exp":  refreshTokenExpired,
		"data": accounts,
	}

	b, _ := json.Marshal(refreshTokenData)
	jsonData := cast.ToString(b)
	//bcryptData, _ := util.BcryptHash(jsonData)
	refreshID := util.HashMd5(jsonData)

	//err = u.repokrakend.SaveRefreshToken(refreshID, jsonData, bcryptData, refreshTokenExpired)
	//log.IfError(err)

	return map[string]interface{}{
		"token":    refreshID,
		"accounts": accounts,
	}, nil
}

func (u authKrakendUseCase) CreateTokenByAccount(userId string, userType string, accountId string) (domain.AuthKrakend, error) {
	var authKrakend domain.AuthKrakend

	if userId == "" || userType == "" {
		log.Info("user id or user type is empty...")
		return authKrakend, errors.New("422")
	}

	//1. check user from DB
	user, err := u.AuthRepository.FindAccountById(userType, userId)
	if user.UserId == "" {
		//account not found
		fmt.Println("account not found: ", userId, userType)
		return authKrakend, errors.New("401")
	}

	log.Info("%s (%s) found with account id: %s | session account id: %s", userId, userType, user.AccountId, accountId)
	if user.AccountId != accountId {
		fmt.Println("have no access to request account")
		return authKrakend, errors.New("403")
	}

	//get generated response format
	authKrakend, err = u.GenerateToken(user)

	//get generated refresh token
	refreshToken, refreshTokenExpired := u.GenerateRefreshToken(user)

	//append generated refresh token to response format
	authKrakend.RefreshToken = refreshToken
	authKrakend.RefreshTokenExpired = refreshTokenExpired

	fmt.Println("refresh token : ", refreshToken)
	return authKrakend, err
}

// Reset password methods - delegate to reset password usecase
func (u authKrakendUseCase) RequestPasswordReset(input domain.ResetPasswordInput) (domain.ResetPasswordResponse, error) {
	return u.resetPasswordUseCase.RequestReset(input)
}

func (u authKrakendUseCase) VerifyPasswordResetOTP(input domain.ResetPasswordInput) (domain.ResetPasswordResponse, error) {
	return u.resetPasswordUseCase.VerifyOTP(input)
}

func (u authKrakendUseCase) UpdatePasswordWithReset(input domain.ResetPasswordInput) (domain.ResetPasswordResponse, error) {
	return u.resetPasswordUseCase.UpdatePassword(input)
}
