package mysql

import (
	"database/sql"
	"errors"
	"time"
	mysql "uniqdev/api-pos-web/core/mysql"
	"uniqdev/api-pos-web/core/util/cast"
	domain "uniqdev/api-pos-web/domain"
)

type resetPasswordRepository struct {
	mysql.Repository
}

func ResetPasswordRepository(db *sql.DB) domain.ResetPasswordRepository {
	return &resetPasswordRepository{mysql.Repository{Conn: db}}
}

// Account operations
func (r resetPasswordRepository) FindAccountByEmail(email string) (domain.User, error) {
	var user domain.User

	// Try admin accounts first
	result, err := r.Query("SELECT id, email, name, phone, 'admin' as user_type, admin_id as user_id, business_id, account_id FROM accounts WHERE email = ? AND is_active = 1 AND user_type = 'admin'", email).Map()
	if err == nil && len(result) > 0 {
		cast.MapToStruct(result, &user)
		return user, nil
	}

	// Try employee accounts
	result, err = r.Query("SELECT id, email, name, phone, 'employee' as user_type, employee_id as user_id, business_id, account_id FROM accounts WHERE email = ? AND is_active = 1 AND user_type = 'employee'", email).Map()
	if err == nil && len(result) > 0 {
		cast.MapToStruct(result, &user)
		return user, nil
	}

	return user, errors.New("account not found")
}

func (r resetPasswordRepository) UpdateAccountPassword(email, hashedPassword string) error {
	_, err := r.Updates("accounts", map[string]interface{}{
		"password":   hashedPassword,
		"updated_at": time.Now().Unix(),
	}, map[string]interface{}{
		"email":     email,
		"is_active": 1,
	})
	return err
}

func (r resetPasswordRepository) InvalidateUserSessions(email string) error {
	// Find user first to get account_id
	user, err := r.FindAccountByEmail(email)
	if err != nil {
		return err
	}

	// Delete all sessions for this user
	_, err = r.Deletes("users_session", map[string]interface{}{
		"data": "%" + user.AccountId + "%",
	})
	return err
}

// OTP operations
func (r resetPasswordRepository) SaveOTP(contact, contactType, hashedOTP string, expiredAt int64) (int64, error) {
	currentTime := time.Now().Unix()

	result, err := r.Insert("auth_otp", map[string]interface{}{
		"contact":      contact,
		"contact_type": contactType,
		"token":        hashedOTP,
		"date_created": currentTime,
		"date_expired": expiredAt,
	})

	if err != nil {
		return 0, err
	}

	id, err := result.LastInsertId()
	return id, err
}

func (r resetPasswordRepository) FindOTPByID(authOtpId int64) (domain.OTPRecord, error) {
	var otp domain.OTPRecord

	result, err := r.Query("SELECT * FROM auth_otp WHERE auth_otp_id = ?", authOtpId).Map()
	if err != nil {
		return otp, err
	}

	if len(result) == 0 {
		return otp, errors.New("OTP record not found")
	}

	cast.MapToStruct(result, &otp)
	return otp, nil
}

func (r resetPasswordRepository) MarkOTPAsAuthenticated(authOtpId int64) error {
	_, err := r.Updates("auth_otp", map[string]interface{}{
		"authenticated_at": time.Now().Unix(),
	}, map[string]interface{}{
		"auth_otp_id": authOtpId,
	})
	return err
}

func (r resetPasswordRepository) CleanupExpiredOTP() error {
	currentTime := time.Now().Unix()
	_, err := r.Deletes("auth_otp", map[string]interface{}{
		"date_expired <": currentTime,
	})
	return err
}

// Reset session operations
func (r resetPasswordRepository) SaveResetSession(sessionId, email, hashedToken string, expiredAt int64) error {
	_, err := r.Insert("users_session", map[string]interface{}{
		"id":         sessionId,
		"data":       email,
		"token":      hashedToken,
		"expired_at": expiredAt,
		"timestamp":  time.Now().Unix(),
	})
	return err
}

func (r resetPasswordRepository) FindResetSession(sessionId string) (domain.ResetSession, error) {
	var session domain.ResetSession

	result, err := r.Query("SELECT id, data as email, token, timestamp as created_at, expired_at FROM users_session WHERE id = ? AND expired_at > ?", sessionId, time.Now().Unix()).Map()
	if err != nil {
		return session, err
	}

	if len(result) == 0 {
		return session, errors.New("reset session not found or expired")
	}

	cast.MapToStruct(result, &session)
	return session, nil
}

func (r resetPasswordRepository) DeleteResetSession(sessionId string) error {
	_, err := r.Deletes("users_session", map[string]interface{}{
		"id": sessionId,
	})
	return err
}

func (r resetPasswordRepository) CleanupExpiredSessions() error {
	currentTime := time.Now().Unix()
	_, err := r.Deletes("users_session", map[string]interface{}{
		"expired_at <": currentTime,
	})
	return err
}

// Message operations
func (r resetPasswordRepository) SaveScheduledMessage(title, message, receiver, media string, timeDeliver int64) error {
	_, err := r.Insert("scheduled_message", map[string]interface{}{
		"title":        title,
		"message":      message,
		"receiver":     receiver,
		"media":        media,
		"time_deliver": timeDeliver,
		"created_at":   time.Now().Unix(),
		"status":       "pending",
	})
	return err
}

// Rate limiting
func (r resetPasswordRepository) CheckResetRateLimit(email string) error {
	// Check if there are more than 3 reset requests in the last hour
	oneHourAgo := time.Now().Add(-time.Hour).Unix()

	result, err := r.Query("SELECT COUNT(*) as count FROM auth_otp WHERE contact = ? AND contact_type = 'email' AND date_created > ?", email, oneHourAgo).Map()
	if err != nil {
		return err
	}

	count := cast.ToInt(result["count"])
	if count >= 3 {
		return errors.New("rate limit exceeded")
	}

	return nil
}
